import { LaunchpadLead, LaunchPadModule, SalesOfferModule } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
// eslint-disable-next-line max-len
import { mainQueue } from '../../../../queues';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { validateEndpoint } from '../applications/helpers';
import { getImpactedUpdates, getSendingUpdates } from './helpers';
import { updateKYCConsentSalesOffer } from './shared';

const mutate: GraphQLMutationResolvers['sendSalesOffer'] = async (
    root,
    { id, endpointId, featureKinds, languageId, consents, customer },
    { loaders, getUser, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();

    const salesOffer = await collections.salesOffers.findOne({
        _id: id,
    });

    if (!salesOffer) {
        throw new Error('Sales offer not found');
    }

    const salesOfferModule = await collections.modules.findOne<SalesOfferModule>({
        _id: salesOffer.moduleId,
    });

    const user = await getUser();

    const existedLead = await collections.leads.findOne<LaunchpadLead>({
        '_versioning.suiteId': salesOffer.leadSuiteId,
        '_versioning.isLatest': true,
    });

    const launchpadModule = await collections.modules.findOne<LaunchPadModule>({
        _id: existedLead.moduleId,
    });

    // get router/endpoint when provided
    const origins = await validateEndpoint(endpointId, launchpadModule);

    // update the kycpresets and consents for COE, Specifications
    const { updatedLead, consentChanges, updatedSalesOffer } = await updateKYCConsentSalesOffer(
        consents,
        customer,
        featureKinds,
        id,
        user,
        loaders
    );

    const updates = await getSendingUpdates(
        {
            salesOfferModule,
            existedLead: updatedLead,
            salesOffer: updatedSalesOffer,
            origins,
            languageId,
            user,
            collections,
            consentChanges,
        },
        featureKinds
    );

    const impactedUpdates = await getImpactedUpdates(updatedSalesOffer, featureKinds, collections, user);

    await collections.salesOffers.findOneAndUpdate(
        {
            _id: id,
        },
        {
            $set: {
                ...updates,
                ...impactedUpdates,
            },
        },
        { returnDocument: 'after' }
    );

    const queueParams = {
        featureKinds,
        salesOfferId: id,
        endpointId,
    };

    await mainQueue.add({
        type: 'onGenerateSalesOfferPdf',
        ...queueParams,
    });

    await mainQueue.add({
        type: 'onSendSalesOfferEmail',
        ...queueParams,
    });

    return updatedLead;
};

export default requiresLoggedUser(mutate);
