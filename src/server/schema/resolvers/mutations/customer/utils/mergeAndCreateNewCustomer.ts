import { uniqWith } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { type Collections } from '../../../../../database/collections';
import { ApplicationKind } from '../../../../../database/documents/Applications';
import { type Customer } from '../../../../../database/documents/Customer';
import type { KYCPreset } from '../../../../../database/documents/KYCPresets';
import type { Lead } from '../../../../../database/documents/Lead';
import { type Module, ModuleType } from '../../../../../database/documents/modules';
import { isMatchingConditions, hasCapQualificationCondition } from '../../../../../database/helpers/conditions';
import { mergeLocalCustomerFields, sortLocalCustomerFields } from '../../../../../database/helpers/customers';
import {
    getCustomerModuleFromApplicationModule,
    getKYCPresetsForCustomerModuleId,
    getKYCPresetsForEvent,
} from '../../../../../database/helpers/kyc';
import { Loaders } from '../../../../../loaders';
import { GraphQLLocalCustomerFieldSettings } from '../../../definitions';
import { calculateCustomerChanges } from '../../applications/submitChanges/updateHandlers/getUpdateHandlers';

type Params = {
    collections: Collections;
    loaders: Loaders;
    newCustomerFieldsSettings: GraphQLLocalCustomerFieldSettings[];
    lead: Lead;
};

const getAllPresets = async (lead: Lead, applicationModule: Module, loaders: Loaders): Promise<KYCPreset[]> => {
    switch (applicationModule._type) {
        case ModuleType.StandardApplicationModule:
        case ModuleType.ConfiguratorModule:
        case ModuleType.MobilityModule:
        case ModuleType.FinderApplicationPublicModule:
        case ModuleType.FinderApplicationPrivateModule:
        case ModuleType.LaunchPadModule:
            return getKYCPresetsForCustomerModuleId(applicationModule.customerModuleId, 'local', loaders);

        case ModuleType.EventApplicationModule: {
            if (lead.kind !== ApplicationKind.Event) {
                throw new Error('Lead kind does not match module type');
            }

            const event = await loaders.eventById.load(lead.eventId);

            if (!event) {
                throw new Error('Event not found');
            }

            const eventKycPresets = await getKYCPresetsForEvent(event, 'local');
            const customerModulePresets = await getKYCPresetsForCustomerModuleId(
                applicationModule.customerModuleId,
                'local',
                loaders
            );

            return [...eventKycPresets, ...customerModulePresets];
        }

        default:
            return [];
    }
};

const mergeAndCreateNewCustomer = async ({
    collections,
    loaders,
    newCustomerFieldsSettings,
    lead,
}: Params): Promise<ObjectId> => {
    const { moduleId: applicationModuleId, customerId: currentCustomerId } = lead;

    const applicationModule = await loaders.moduleById.load(applicationModuleId);
    const company = await loaders.companyById.load(applicationModule.companyId);
    const customer = await loaders.customerById.load(currentCustomerId);

    const newCustomerFields = await calculateCustomerChanges(
        newCustomerFieldsSettings,
        company._id,
        loaders,
        currentCustomerId
    );
    const mergedCustomerFields = mergeLocalCustomerFields(customer.fields, newCustomerFields);

    // Get KYC presets for qualification
    const customerModule = await getCustomerModuleFromApplicationModule(applicationModule, loaders);
    if (!customerModule) {
        throw new Error('Customer module not found');
    }

    /**
     * Retrieve all KYC presets for the lead.
     * Including presets under event and event.customerModuleId.
     */
    const initialPresets = await getAllPresets(lead, applicationModule, loaders);
    const qualificationPresets = initialPresets.filter(
        preset =>
            hasCapQualificationCondition(preset.conditions ?? []) &&
            isMatchingConditions(preset.conditions ?? [], {
                customerKind: 'local',
                isCapQualification: true,
                applicationModuleId,
            })
    );

    const kycPresetIds = qualificationPresets.map(preset => preset._id);

    const sortedCustomerFields = sortLocalCustomerFields(
        customerModule.kycFields.sort((a, b) => a.order - b.order),
        initialPresets,
        mergedCustomerFields
    );

    await collections.customers.updateMany(
        { '_versioning.suiteId': customer._versioning.suiteId },
        { $set: { '_versioning.isLatest': false } }
    );

    const newCustomer: Customer = {
        ...customer,
        _id: new ObjectId(),
        kycPresetIds: uniqWith((a, b) => a.equals(b), [...(customer.kycPresetIds || []), ...kycPresetIds]),
        fields: sortedCustomerFields,
        _versioning: {
            ...customer._versioning,
            isLatest: true,
        },
    };

    await collections.customers.insertOne(newCustomer);

    return newCustomer._id;
};

export default mergeAndCreateNewCustomer;
