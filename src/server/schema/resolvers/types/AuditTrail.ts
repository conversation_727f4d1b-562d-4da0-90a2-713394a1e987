import dayjs from 'dayjs';
import { TFunction } from 'i18next';
import { capitalize, get, isBoolean, isEmpty, isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import numeral from 'numeral';
import {
    ApplicationStage,
    AuditTrailKind,
    Author,
    AuthorKind,
    ChangeKind,
    ConfiguratorInventoryStockUpdatedAuditTrail,
    ConfiguratorUpsertedAuditTrail,
    getBankIdFromApplication,
    getCustomerFullName,
    getDealerIdFromApplication,
    getMobilityLocationName,
    MobilityBookingLocation,
    MobilityBookingLocationType,
    MobilityInventoryStockUpdatedAuditTrail,
    MobilityUpsertedAuditTrail,
    MonthlyInstalment,
    Period,
    MaskDirection,
    ApplicationJourney,
    getKYCPresetsForCustomerModule,
} from '../../../database';
import { AudienceMessage } from '../../../emails';
import formatMonthlyInstalment, { getMonthlyInstalmentLabels } from '../../../emails/utils/formatMonthlyInstalment';
import { getFormats } from '../../../emails/utils/useFormats';
import { Loaders } from '../../../loaders';
import { DEFAULT_STAGES, getApplicationAssigneeId } from '../../../utils/application';
import applyDataMasking from '../../../utils/maskInfo';
import { ApplicationKind, GraphQLAuditTrailResolvers, ModuleType } from '../definitions';

const getReferenceIdentifier = async (
    applicationId: ObjectId,
    fromStage: ApplicationStage,
    loaders: Loaders,
    getReferenceSuiteId: (journey: ApplicationJourney) => ObjectId
) => {
    try {
        const application = await loaders.applicationById.load(applicationId);
        if (!application) {
            return '';
        }

        const journey = await loaders.applicationJourneyBySuiteId.load(application._versioning.suiteId);

        const referenceSuiteId = getReferenceSuiteId(journey);
        if (!journey || !referenceSuiteId) {
            return '';
        }

        const reference = await loaders.applicationBySuiteId.load(referenceSuiteId);
        const lead = await loaders.leadById.load(application.leadId);

        switch (fromStage) {
            case ApplicationStage.Reservation:
                return reference.reservationStage?.identifier || '';

            case ApplicationStage.Lead:
                return lead?.identifier || '';

            case ApplicationStage.Insurance:
                return reference.insuranceStage?.identifier || '';

            case ApplicationStage.Appointment:
                return reference.appointmentStage?.identifier || '';

            case ApplicationStage.Financing:
                return reference.financingStage?.identifier || '';

            default:
                return '';
        }
    } catch {
        return '';
    }
};

const getLatestCustomerNameWithMasking = async (
    customerId: ObjectId,
    loaders: Loaders,
    t: TFunction
): Promise<string | null> => {
    // For author customer, it's possible that the customer is not in latest version (draft phase)
    // So we need to retrieve it from suiteId
    const customer = await loaders.customerById.load(customerId);

    const latestCustomer = !customer?._versioning?.isLatest
        ? await loaders.customerByLatestSuiteId.load(customer._versioning.suiteId)
        : customer;

    // If not found, just return fallback
    if (!latestCustomer) {
        return null;
    }

    // Check also for masking, same behavior with customer resolver
    const customerModule = await loaders.moduleById.load(latestCustomer.moduleId);
    if (customerModule._type !== ModuleType.LocalCustomerManagement) {
        return null;
    }
    const company = await loaders.companyById.load(customerModule.companyId);
    const maskSetting = company?.mask;

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);
    const customerFullName = getCustomerFullName(t, latestCustomer, company, kycPresets);
    if (maskSetting.direction !== MaskDirection.None) {
        return applyDataMasking(customerFullName, company.mask.count, company.mask.direction);
    }

    return customerFullName;
};

export const formatAuthorText = (t: TFunction, inputs: null | [string, AuthorKind]) => {
    if (!inputs) {
        return '';
    }

    const [author, kind] = inputs;

    switch (kind) {
        case AuthorKind.Bank:
            return `${author} (${t('auditTrails:author.bank')})`;

        case AuthorKind.User:
            return `${author} (${t('auditTrails:author.user')})`;

        case AuthorKind.Customer:
            return `${author} (${t('auditTrails:author.customer')})`;

        case AuthorKind.Insurer:
            return `${author} (${t('auditTrails:author.insurer')})`;

        case AuthorKind.System:
            return t('auditTrails:author.system');

        case AuthorKind.Salesforce:
            return author;

        default:
            return '';
    }
};

const formatAuthor = async (
    author: Author,
    t: TFunction,
    loaders: Loaders,
    customerKind?: 'applicant' | 'guarantor',
    applicationId?: ObjectId
): Promise<null | [string, AuthorKind]> => {
    switch (author.kind) {
        case AuthorKind.Bank: {
            const bank = await loaders.bankById.load(author.id);

            return [bank.displayName, AuthorKind.Bank];
        }

        case AuthorKind.User: {
            const user = await loaders.userById.load(author.id);

            return [user.displayName, AuthorKind.User];
        }

        case AuthorKind.System: {
            return [t('auditTrails:author.system'), AuthorKind.System];
        }

        case AuthorKind.Customer: {
            const customerName = await getLatestCustomerNameWithMasking(author.id, loaders, t);

            switch (customerKind) {
                case 'applicant':
                    return [customerName ?? t('auditTrails:author.applicant'), AuthorKind.Customer];

                case 'guarantor':
                    return [customerName ?? t('auditTrails:author.guarantor'), AuthorKind.Customer];

                default:
                    return [customerName ?? t('auditTrails:author.customer'), AuthorKind.Customer];
            }
        }

        case AuthorKind.Insurer: {
            const insurer = await loaders.insurerById.load(author.id);

            return [insurer.displayName, AuthorKind.Insurer];
        }

        default:
            return null;
    }
};

const fromAudience = async (
    audience: AudienceMessage,
    t: TFunction,
    loaders: Loaders,
    leadId?: ObjectId,
    applicationId?: ObjectId,
    assigneeId?: ObjectId
) => {
    if (!leadId && !applicationId) {
        throw new Error('Either leadId or applicationId must be provided');
    }

    switch (audience) {
        case AudienceMessage.Customer: {
            if (leadId) {
                const lead = await loaders.leadById.load(leadId);
                const customerName = await getLatestCustomerNameWithMasking(lead.customerId, loaders, t);

                return [customerName, t('auditTrails:author.customer')];
            }

            const application = applicationId ? await loaders.applicationById.load(applicationId) : null;
            const lead = application ? await loaders.leadById.load(application.leadId) : null;
            const customerName = lead ? await getLatestCustomerNameWithMasking(lead.customerId, loaders, t) : null;

            return [customerName, t('auditTrails:author.customer')];
        }

        case AudienceMessage.Salesperson: {
            const user = assigneeId ? await loaders.userById.load(assigneeId) : null;
            if (user) {
                return [user.displayName, t('auditTrails:author.scUser')];
            }

            return ['', t('auditTrails:author.scUser')];
        }

        case AudienceMessage.Bank: {
            const application = await loaders.applicationById.load(applicationId);
            const bankId = getBankIdFromApplication(application);
            const bank = bankId ? await loaders.bankById.load(bankId) : null;

            return [bank?.displayName, AudienceMessage.Bank];
        }

        case AudienceMessage.Dealer: {
            const application = await loaders.applicationById.load(applicationId);
            const dealerId = getDealerIdFromApplication(application);
            const dealer = dealerId ? await loaders.dealerById.load(dealerId) : null;

            return [dealer.displayName, AudienceMessage.Dealer];
        }

        case AudienceMessage.Insurer: {
            const application = await loaders.applicationById.load(applicationId);
            if (
                (application.kind === ApplicationKind.Configurator ||
                    application.kind === ApplicationKind.Standard ||
                    application.kind === ApplicationKind.Finder) &&
                application.insurancing.insurerId
            ) {
                const insurer = await loaders.insurerById.load(application.insurancing.insurerId);

                return [insurer.displayName, AudienceMessage.Insurer];
            }

            return null;
        }

        case AudienceMessage.Operator: {
            const application = await loaders.applicationById.load(applicationId);

            if (application.kind === ApplicationKind.Mobility) {
                return [
                    getMobilityLocationName(t, application.mobilityBookingDetails.location),
                    AudienceMessage.Dealer,
                ];
            }

            return null;
        }

        default:
            return null;
    }
};

const getTranslatedPeriod = (period: Period | null | undefined, t: TFunction) => {
    if (!period) {
        return null;
    }

    const start = dayjs(period.start);
    const end = dayjs(period.end);

    const from = start.format(t('common:formats.dateTimeFormat'));
    const to = end.format(t('common:formats.dateTimeFormat'));

    return t('common:period', { from, to });
};

const getTranslatedPeriodByTimeZone = (period: Period | null | undefined, t: TFunction, timeZone: string) => {
    if (!period) {
        return null;
    }

    const start = dayjs(period.start).tz(timeZone);
    const end = dayjs(period.end).tz(timeZone);

    const from = start.format(t('common:formats.dateTimeFormat'));
    const to = end.format(t('common:formats.dateTimeFormat'));

    return t('common:period', { from, to });
};

const getTranslatedDateTimeByTimeZone = (dateTime: Date | null | undefined, t: TFunction, timeZone: string) => {
    if (!dateTime) {
        return null;
    }

    return dayjs(dateTime).tz(timeZone).format(t('common:formats.dateTimeFormat'));
};

const getTranslatedDateByTimeZone = (dateTime: Date | null | undefined, t: TFunction, timeZone: string) => {
    if (!dateTime) {
        return null;
    }

    return dayjs(dateTime).tz(timeZone).format(t('common:formats.datePicker'));
};

const getInventoryTranslationSuffix = (oldValue, newValue) => {
    if ((isNil(oldValue) || isEmpty(oldValue)) && (isNil(newValue) || isEmpty(newValue))) {
        return '';
    }

    if (oldValue) {
        return '_remove';
    }

    if (newValue) {
        return '_add';
    }

    return '';
};

const isEmptyValue = value =>
    value === undefined ||
    value === null ||
    Number.isNaN(value) ||
    (typeof value === 'number' && false) ||
    (typeof value === 'object' && Object.keys(value).length === 0) ||
    (typeof value === 'string' && value.trim() === '') ||
    (typeof value === 'boolean' && false);

const getInventoryStockTranslationSuffix = (oldValue, newValue) => {
    if (oldValue && isEmptyValue(newValue)) {
        return '_remove';
    }

    if (isEmptyValue(oldValue) && newValue) {
        return '_add';
    }

    return '';
};

const formatMonthlyInstallment = (monthlyInstalment: MonthlyInstalment[], formatAmountWithCurrency, t) => {
    if (monthlyInstalment.length === 1) {
        return formatMonthlyInstalment(monthlyInstalment, formatAmountWithCurrency, t);
    }

    const [initialLabel, finalLabel] = getMonthlyInstalmentLabels(
        monthlyInstalment[0].end,
        monthlyInstalment[1].end,
        t
    );

    return `${formatAmountWithCurrency(monthlyInstalment[0].amount)} - ${initialLabel}/${formatAmountWithCurrency(
        monthlyInstalment[1].amount
    )} - ${finalLabel}`;
};

const getYesNoTranslation = (isTrue: boolean, t: TFunction) =>
    isTrue ? t('common:options.yesNo.yes') : t('common:options.yesNo.no');

const getUpdate = (changes, { t, formatPercentage, formatAmount, formatAmountWithCurrency, company }) =>
    (changes ?? [])
        .map(change => {
            const { key, before, after, kind } = change;
            let from;
            let to;

            switch (kind) {
                case ChangeKind.String:
                    from = isNil(before) ? t('auditTrails:formats.empty') : before;
                    to = isNil(after) ? t('auditTrails:formats.empty') : after;
                    break;

                case ChangeKind.Number:
                    from = isNil(before) ? t('auditTrails:formats.empty') : before;
                    to = isNil(after) ? t('auditTrails:formats.empty') : after;
                    break;

                case ChangeKind.Percent:
                    from = isNil(before) ? t('auditTrails:formats.empty') : formatPercentage(before);
                    to = isNil(after) ? t('auditTrails:formats.empty') : formatPercentage(after);
                    break;

                case ChangeKind.Amount:
                    from = isNil(before) ? t('auditTrails:formats.empty') : formatAmount(before);
                    to = isNil(after) ? t('auditTrails:formats.empty') : formatAmount(after);
                    break;

                case ChangeKind.Currency:
                    from = isNil(before) ? t('auditTrails:formats.empty') : formatAmountWithCurrency(before);
                    to = isNil(after) ? t('auditTrails:formats.empty') : formatAmountWithCurrency(after);
                    break;

                case ChangeKind.Period:
                    from =
                        isNil(before?.start) || isNil(before?.end)
                            ? t('auditTrails:formats.empty')
                            : getTranslatedPeriodByTimeZone(before as Period, t, company.timeZone);

                    to =
                        isNil(after?.start) || isNil(after?.end)
                            ? t('auditTrails:formats.empty')
                            : getTranslatedPeriodByTimeZone(after as Period, t, company.timeZone);
                    break;

                case ChangeKind.MobilityLocation: {
                    const getLocationName = (location: MobilityBookingLocation) => {
                        switch (location._type) {
                            case MobilityBookingLocationType.Home:
                                return t('common:homeDelivery');

                            case MobilityBookingLocationType.Pickup:
                                return location.name;

                            default:
                                return undefined;
                        }
                    };

                    from = getLocationName(before) ?? t('auditTrails:formats.empty');
                    to = getLocationName(after) ?? t('auditTrails:formats.empty');

                    break;
                }

                case ChangeKind.Date:
                    from = isNil(before)
                        ? t('auditTrails:formats.empty')
                        : getTranslatedDateByTimeZone(before as Date, t, company.timeZone);

                    to = isNil(after)
                        ? t('auditTrails:formats.empty')
                        : getTranslatedDateByTimeZone(after as Date, t, company.timeZone);
                    break;

                case ChangeKind.DateTime:
                    from = isNil(before)
                        ? t('auditTrails:formats.empty')
                        : getTranslatedDateTimeByTimeZone(before as Date, t, company.timeZone);

                    to = isNil(after)
                        ? t('auditTrails:formats.empty')
                        : getTranslatedDateTimeByTimeZone(after as Date, t, company.timeZone);
                    break;

                case ChangeKind.CompoundValue:
                    from = isNil(before)
                        ? t('auditTrails:formats.empty')
                        : `${formatAmountWithCurrency(before?.amount)} (${formatPercentage(before?.percentage)})`;

                    to = isNil(after)
                        ? t('auditTrails:formats.empty')
                        : `${formatAmountWithCurrency(after?.amount)} (${formatPercentage(after?.percentage)})`;
                    break;

                case ChangeKind.DealerOption:
                    from = isNil(before)
                        ? t('auditTrails:formats.empty')
                        : `${formatAmountWithCurrency(
                              before?.reduce((total, { amount }) => total + (amount || 0), 0) ?? 0
                          )}`;

                    to = isNil(after)
                        ? t('auditTrails:formats.empty')
                        : `${formatAmountWithCurrency(
                              after?.reduce((total: any, { amount }: any) => total + (amount || 0), 0) ?? 0
                          )}`;
                    break;

                case ChangeKind.MonthlyInstalment:
                    from = isNil(before)
                        ? t('auditTrails:formats.empty')
                        : formatMonthlyInstallment(before, formatAmountWithCurrency, t);
                    to = isNil(after)
                        ? t('auditTrails:formats.empty')
                        : formatMonthlyInstallment(after, formatAmountWithCurrency, t);
                    break;

                case ChangeKind.Phone:
                    from = isNil(before) ? t('auditTrails:formats.empty') : `+${before.prefix}${before.value}`;
                    to = isNil(after) ? t('auditTrails:formats.empty') : `+${after.prefix}${after.value}`;
                    break;

                case ChangeKind.StringDescription:
                    from = isNil(before) ? t('auditTrails:formats.empty') : before.value;
                    to = isNil(after) ? t('auditTrails:formats.empty') : after.value;
                    break;

                case ChangeKind.UploadedFileWithPreview:
                    from = isNil(before) ? t('auditTrails:formats.empty') : before.map(i => i.filename).join('/');
                    to = isNil(after) ? t('auditTrails:formats.empty') : after.map(i => i.filename).join('/');
                    break;
            }

            return t('auditTrails:formats.change', { label: t(key), from, to });
        })
        .join(', ');

const renderAuditTrailMessage: GraphQLAuditTrailResolvers['message'] = async (
    root,
    args,
    { loaders, getTranslations }
) => {
    const { t } = await getTranslations([
        'auditTrails',
        'common',
        'inventoryDetails',
        'applicationDetails',
        'financeProductDetails',
        'calculation',
        'calculators',
        'customerDetails',
        'inventoryDetails',
        'lead',
    ]);

    switch (root._kind) {
        case AuditTrailKind.ApplicationAmendments: {
            const { applicationId } = root;
            const application = await loaders.applicationById.load(applicationId);
            const module = await loaders.moduleById.load(application.moduleId);
            const company = await loaders.companyById.load(module.companyId);
            const { formatAmount, formatAmountWithCurrency, formatPercentage } = getFormats(
                company?.currency || '',
                company?.roundings?.amount?.decimals || 0,
                company?.roundings?.percentage?.decimals || 0
            );

            const { changes } = root;
            const updates = getUpdate(changes, {
                t,
                formatPercentage,
                formatAmount,
                formatAmountWithCurrency,
                company,
            });

            const [author] = await formatAuthor(root.author, t, loaders);

            return t(`auditTrails:application.${root._kind}`, {
                author,
                updates,
            });
        }

        case AuditTrailKind.ApplicationRemarksSubmitted: {
            return t(`auditTrails:application.${root._kind}`, {
                remarks: root.remarks,
            });
        }

        case AuditTrailKind.ApplicationSubmittedToSystem:
        case AuditTrailKind.ApplicationResubmittedToSystem:
        case AuditTrailKind.ApplicationKYCReceived:
        case AuditTrailKind.ApplicationTestDriveKYCAuditTrail:
        case AuditTrailKind.ApplicationFinancingRequested:
        case AuditTrailKind.BookingSubmitted:
        case AuditTrailKind.BookingAmended: {
            return t(`auditTrails:application.${root._kind}`);
        }

        case AuditTrailKind.ApplicationSubmittedToBank:
        case AuditTrailKind.ApplicationResubmittedToBank: {
            const { reference, bankId } = root;
            const options = {
                bank: bankId ? (await loaders.bankById.load(bankId))?.displayName : null,
                reference,
            };

            return t(
                `auditTrails:application.${root._kind}.${isEmpty(reference) ? 'withoutReference' : 'withReference'}`,
                options
            );
        }

        case AuditTrailKind.ApplicationSubmissionToBankFailed:
        case AuditTrailKind.ApplicationResubmissionToBankFailed: {
            return t(`auditTrails:application.${root._kind}`, {
                bank: (await loaders.bankById.load(root.bankId))?.displayName,
                reason: root.reason,
            });
        }

        case AuditTrailKind.ApplicationApproved: {
            const {
                approvedAmount,
                applicationId,
                author,
                approvedTenure,
                approvedInterestRate,
                effectiveInterestRate,
            } = root;

            const authors = await formatAuthor(author, t, loaders);
            const formattedAuthor = formatAuthorText(t, authors);

            if (!approvedAmount) {
                return t(`auditTrails:application.${root._kind}.withoutAmount`, { author: formattedAuthor });
            }

            const application = await loaders.applicationById.load(applicationId);
            const module = await loaders.moduleById.load(application.moduleId);
            const company = await loaders.companyById.load(module.companyId);

            if (approvedTenure && approvedInterestRate) {
                const common = {
                    author: formattedAuthor,
                    amount: `${company.currency} ${numeral(approvedAmount).format('0,0[.][0000]')}`,
                    tenure: approvedTenure,
                    interestRate: approvedInterestRate,
                };

                if (effectiveInterestRate) {
                    return t(`auditTrails:application.${root._kind}.withExtraDetails`, {
                        ...common,
                        effectiveInterestRate,
                    });
                }

                return t(`auditTrails:application.${root._kind}.withDetails`, common);
            }

            return t(`auditTrails:application.${root._kind}.withAmount`, {
                author: formattedAuthor,
                amount: `${company.currency} ${numeral(approvedAmount).format('0,0[.][0000]')}`,
            });
        }

        case AuditTrailKind.ApplicationCancelled: {
            const { reference, applicationId } = root;

            const authors = await formatAuthor(root.author, t, loaders, undefined, applicationId);
            const options = {
                author: formatAuthorText(t, authors),
                reference,
            };

            return t(
                `auditTrails:application.${root._kind}.${isEmpty(reference) ? 'withoutReference' : 'withReference'}`,
                options
            );
        }

        case AuditTrailKind.ApplicationCancellationToBankFailed: {
            return t(`auditTrails:application.${root._kind}`, {
                reason: root.reason,
            });
        }

        // Audit trail with author format text only
        case AuditTrailKind.ApplicationDrafted:
        case AuditTrailKind.ApplicationCustomerAgreedOnCnD:
        case AuditTrailKind.ApplicationTestDriveCnDAuditTrail:
        case AuditTrailKind.ApplicationShowroomVisitCnDAuditTrail:
        case AuditTrailKind.InsuranceApplicationApproved:
        case AuditTrailKind.InsuranceApplicationDeclined:
        case AuditTrailKind.InsuranceApplicationCancelled:
        case AuditTrailKind.ApplicationCompleted:
        case AuditTrailKind.ApplicationDeclined:
        case AuditTrailKind.ApplicationShared:
        case AuditTrailKind.AgreementConcluded:
        case AuditTrailKind.ApplicationPaymentSkipped:
        case AuditTrailKind.ApplicationPaymentCapturing:
        case AuditTrailKind.RequestReleaseLetter:
        case AuditTrailKind.RequestDisbursement:
        case AuditTrailKind.ApplicationConfirmedBookingAuditTrail:
        case AuditTrailKind.ApplicationContacted:
        case AuditTrailKind.ApplicationTestDriveStartedAuditTrail:
        case AuditTrailKind.ApplicationTestDriveEndedAuditTrail:
        case AuditTrailKind.ApplicationCheckIn:
        case AuditTrailKind.ApplicationAppointmentMade:
        case AuditTrailKind.ApplicationProceedWithCustomerDevice: {
            const authors = await formatAuthor(root.author, t, loaders, undefined, root.applicationId);

            return t(`auditTrails:application.${root._kind}`, {
                author: formatAuthorText(t, authors),
            });
        }

        case AuditTrailKind.BankCustomerInfoReceived: {
            const { source, bankId } = root;

            if (!source) {
                return t(`auditTrails:application.${root._kind}.withoutSource`, {
                    bank: (await loaders.bankById.load(bankId))?.displayName,
                });
            }

            return t(`auditTrails:application.${root._kind}.withSource`, {
                bank: (await loaders.bankById.load(bankId))?.displayName,
                source: t(`auditTrails:application.${root._kind}.sources.${source}`),
            });
        }

        case AuditTrailKind.BankReviewInProgress: {
            const { bankId, subDescription } = root;
            const bank = (await loaders.bankById.load(bankId))?.displayName;

            if (isEmpty(subDescription)) {
                return t(`auditTrails:application.${root._kind}.withoutSubDescription`, {
                    bank,
                });
            }

            return t(`auditTrails:application.${root._kind}.withSubDescription`, {
                bank,
                subDescription,
            });
        }

        case AuditTrailKind.ReassignBankAssignee: {
            const { bankId, success } = root;
            const bank = (await loaders.bankById.load(bankId))?.displayName;

            return t(`auditTrails:application.${root._kind}.${success ? 'success' : 'fail'}`, { bank });
        }

        case AuditTrailKind.ApplicationPaymentPending:
        case AuditTrailKind.ApplicationRetrieveStatus:
        case AuditTrailKind.ApplicationPaymentCompleted:
        case AuditTrailKind.ApplicationPaymentFailed:
        case AuditTrailKind.ApplicationPaymentRefunded: {
            return t(`auditTrails:application.${root._kind}`);
        }

        case AuditTrailKind.RequestDisbursementFail: {
            return t(`auditTrails:application.${root._kind}`, { reason: root.reason });
        }

        case AuditTrailKind.ApplicationApplyForFinanceCreation: {
            const referenceIdentifier = await getReferenceIdentifier(
                root.applicationId,
                root.fromStage,
                loaders,
                journey => journey.applyingForFinance?.referenceSuiteId
            );

            return t(`auditTrails:application.${root._kind}`, {
                stage: capitalize(root.fromStage),
                referenceIdentifier,
            });
        }

        case AuditTrailKind.ApplicationApplyForReservationCreation: {
            const referenceIdentifier = await getReferenceIdentifier(
                root.applicationId,
                root.fromStage,
                loaders,
                journey => journey.applyingForFinance?.referenceSuiteId
            );

            return t(`auditTrails:application.${root._kind}`, {
                stage: capitalize(root.fromStage),
                referenceIdentifier,
            });
        }

        case AuditTrailKind.ApplicationAppointmentEndTestDriveReminder: {
            const application = root.applicationId ? await loaders.applicationById.load(root.applicationId) : null;
            const lead = application ? await loaders.leadById.load(application.leadId) : null;
            const assigneeId = application
                ? getApplicationAssigneeId(application, lead, DEFAULT_STAGES)
                : lead.assigneeId;

            const [recipient, recipientType] = await fromAudience(
                root.audience,
                t,
                loaders,
                application?.leadId,
                root.applicationId,
                assigneeId
            );

            return recipient
                ? t(`auditTrails:application.${root._kind}`, { recipient, recipientType })
                : t(`auditTrails:application.applicationCommonEmailSent`, { recipientType });
        }
        case AuditTrailKind.ApplicationEmailSent:
        case AuditTrailKind.BookingConfirmationEmailSent:
        case AuditTrailKind.BookingCancellationEmailSent:
        case AuditTrailKind.BookingAmendmentEmailSent:
        case AuditTrailKind.ReminderEmailSent:
        case AuditTrailKind.BookingCompletionEmailSent: {
            const [recipient, recipientType] = await fromAudience(
                root.audience,
                t,
                loaders,
                null,
                root.applicationId,
                root._kind === AuditTrailKind.ApplicationEmailSent && root.assigneeId
            );

            return recipient
                ? t(`auditTrails:application.${root._kind}`, { recipient, recipientType })
                : t(`auditTrails:application.applicationCommonEmailSent`, { recipientType });
        }

        case AuditTrailKind.ApplicationApplyForInsuranceCreation: {
            const referenceIdentifier = await getReferenceIdentifier(
                root.applicationId,
                root.fromStage,
                loaders,
                journey => journey.applyingForInsurance?.referenceSuiteId
            );

            return t(`auditTrails:application.${root._kind}`, {
                stage: capitalize(root.fromStage),
                referenceIdentifier,
            });
        }

        case AuditTrailKind.ApplicationSubmissionToInsuranceCompanyFailed: {
            const insurerName = (await loaders.insurerById.load(root.insurerId))?.displayName;

            return t(`auditTrails:application.${root._kind}`, {
                reason: root.reason,
                insurerName,
            });
        }

        case AuditTrailKind.ApplicationSubmittedToInsuranceCompany: {
            const insurer = await loaders.insurerById.load(root.insurerId);

            const referenceKey = !isEmpty(root.reference) ? 'withReference' : 'withoutReference';

            return t(`auditTrails:application.${root._kind}.${referenceKey}`, {
                insurerName: insurer?.displayName,
                reference: root.reference,
            });
        }

        case AuditTrailKind.ApplicationComment: {
            const [author] = await formatAuthor(root.author, t, loaders);

            return t(`auditTrails:application.${root._kind}`, { author, comment: root.comment });
        }

        case AuditTrailKind.ApplicationSigningInitiated:
        case AuditTrailKind.ApplicationSigningRejected:
        case AuditTrailKind.ApplicationOTPInitiated:
        case AuditTrailKind.ApplicationSigningCompleted:
        case AuditTrailKind.ApplicationTestDriveOTPInitiated:
        case AuditTrailKind.ApplicationTestDriveSigningInitiated:
        case AuditTrailKind.ApplicationTestDriveSigningRejected:
        case AuditTrailKind.ApplicationTestDriveSigningCompleted: {
            return t(`auditTrails:application.${root._kind}`, { author: capitalize(root.customerKind) });
        }

        case AuditTrailKind.ApplicationOTPCompleted:
        case AuditTrailKind.ApplicationTestDriveOTPCompleted: {
            if (isNil(root.ipAddress)) {
                return t(`auditTrails:application.applicationOTPCompleted`, { author: capitalize(root.customerKind) });
            }

            return t(`auditTrails:application.applicationOTPCompletedWithIp`, {
                author: capitalize(root.customerKind),
                IPAddress: root.ipAddress,
            });
        }

        case AuditTrailKind.InventoryComment: {
            const [author] = await formatAuthor(root.author, t, loaders);

            return t(`auditTrails:inventory.${root._kind}`, { author, comment: root.comment });
        }

        case AuditTrailKind.ConfiguratorInventoryCreated:
        case AuditTrailKind.MobilityInventoryCreated:
        case AuditTrailKind.ConfiguratorInventoryDeleted:
        case AuditTrailKind.MobilityInventoryDeleted: {
            const [author] = await formatAuthor(root.author, t, loaders);

            return t(`auditTrails:inventory.${root._kind}`, { author });
        }

        case AuditTrailKind.ConfiguratorInventoryUpdated: {
            const [author] = await formatAuthor(root.author, t, loaders);

            const inventoryAuditTrails: ConfiguratorUpsertedAuditTrail[] = (
                await loaders.auditTrailsByInventoryId.load(root.inventoryId)
            ).filter(
                trail =>
                    trail._kind === AuditTrailKind.ConfiguratorInventoryCreated ||
                    trail._kind === AuditTrailKind.ConfiguratorInventoryUpdated
            ) as ConfiguratorUpsertedAuditTrail[];

            const index = inventoryAuditTrails.findIndex(trail => trail._id.equals(root._id));

            const oldInventory = inventoryAuditTrails[index - 1];
            const inventory = inventoryAuditTrails[index];

            return t(`auditTrails:inventory.${root._kind}`, {
                author,
                oldValue: getYesNoTranslation(oldInventory?.isActive, t),
                newValue: getYesNoTranslation(inventory?.isActive, t),
                field: t(`inventoryDetails:fields.isActive.label`),
            });
        }

        case AuditTrailKind.MobilityInventoryUpdated: {
            const [author] = await formatAuthor(root.author, t, loaders);

            const inventoryAuditTrails: MobilityUpsertedAuditTrail[] = (
                await loaders.auditTrailsByInventoryId.load(root.inventoryId)
            ).filter(
                trail =>
                    trail._kind === AuditTrailKind.MobilityInventoryCreated ||
                    trail._kind === AuditTrailKind.MobilityInventoryUpdated
            ) as MobilityUpsertedAuditTrail[];

            const index = inventoryAuditTrails.findIndex(trail => trail._id.equals(root._id));
            const oldInventory = inventoryAuditTrails[index - 1];
            const inventory = inventoryAuditTrails[index];

            const keys = ['isActive', 'period'];

            return keys
                .map(inventoryKey => {
                    const field = t(`inventoryDetails:fields.${inventoryKey}.label`);

                    if (inventoryKey === 'period') {
                        const currentPeriod = get(inventoryKey, oldInventory);
                        const newPeriod = get(inventoryKey, inventory);

                        if (
                            currentPeriod?.start?.getTime() === newPeriod?.start?.getTime() &&
                            currentPeriod?.end?.getTime() === newPeriod?.end?.getTime()
                        ) {
                            return null;
                        }
                    }
                    if (get(inventoryKey, oldInventory) === get(inventoryKey, inventory)) {
                        return null;
                    }

                    if (inventoryKey === 'period') {
                        return t(
                            `auditTrails:inventory.${root._kind}${getInventoryTranslationSuffix(
                                get(inventoryKey, oldInventory),
                                get(inventoryKey, inventory)
                            )}`,
                            {
                                author,
                                oldValue: getTranslatedPeriod(get(inventoryKey, oldInventory), t),
                                newValue: getTranslatedPeriod(get(inventoryKey, inventory), t),
                                field,
                            }
                        );
                    }
                    if (inventoryKey === 'isActive') {
                        return t(
                            `auditTrails:inventory.${root._kind}${getInventoryTranslationSuffix(
                                get(inventoryKey, oldInventory),
                                get(inventoryKey, inventory)
                            )}`,
                            {
                                author,
                                oldValue: getYesNoTranslation(get(inventoryKey, oldInventory), t),
                                newValue: getYesNoTranslation(get(inventoryKey, inventory), t),
                                field,
                            }
                        );
                    }

                    return t(
                        `auditTrails:inventory.${root._kind}${getInventoryTranslationSuffix(
                            get(inventoryKey, oldInventory),
                            get(inventoryKey, inventory)
                        )}`,
                        {
                            author,
                            oldValue: get(inventoryKey, oldInventory),
                            newValue: get(inventoryKey, inventory),
                            field,
                        }
                    );
                })
                .filter(Boolean)
                .join('\n');
        }

        case AuditTrailKind.InventoryStockAdded:
        case AuditTrailKind.InventoryStockDeleted: {
            const [author] = await formatAuthor(root.author, t, loaders);

            if (root.last) {
                return t(`auditTrails:inventory.${root._kind}`, {
                    author,
                    first: root.first,
                    last: root.last,
                });
            }

            return t(`auditTrails:inventory.${root._kind}_single`, {
                author,
                first: root.first,
            });
        }

        case AuditTrailKind.ConfiguratorInventoryStockUpdated: {
            const [author] = await formatAuthor(root.author, t, loaders);

            const inventoryAuditTrails: ConfiguratorInventoryStockUpdatedAuditTrail[] = (
                await loaders.auditTrailsByInventoryId.load(root.inventoryId)
            ).filter(
                trail =>
                    trail._kind === AuditTrailKind.ConfiguratorInventoryStockUpdated &&
                    trail.stockIdentifier === root.stockIdentifier
            ) as ConfiguratorInventoryStockUpdatedAuditTrail[];

            const index = inventoryAuditTrails.findIndex(trail => trail._id.equals(root._id));
            const inventory = inventoryAuditTrails[index];

            const keys = ['vin'] as const;

            return keys
                .map(inventoryKey => {
                    const field = t(`inventoryDetails:stocks.${inventoryKey}.label`);

                    const base = {
                        author,
                        newValue: inventory[inventoryKey],
                        field,
                        identifier: root.stockIdentifier,
                    };

                    if (index === 0) {
                        if (
                            isNil(inventory[inventoryKey]) ||
                            (!isBoolean(inventory[inventoryKey]) && isEmpty(inventory[inventoryKey]))
                        ) {
                            return null;
                        }

                        return t(`auditTrails:inventory.${root._kind}_add`, base);
                    }

                    const oldInventory = inventoryAuditTrails[index - 1];

                    if (oldInventory[inventoryKey] === inventory[inventoryKey]) {
                        return null;
                    }

                    return t(
                        `auditTrails:inventory.${root._kind}${getInventoryTranslationSuffix(
                            oldInventory[inventoryKey],
                            inventory[inventoryKey]
                        )}`,
                        {
                            ...base,
                            oldValue: oldInventory[inventoryKey],
                        }
                    );
                })
                .filter(Boolean)
                .join('\n');
        }

        case AuditTrailKind.MobilityInventoryStockUpdated: {
            const [author] = await formatAuthor(root.author, t, loaders);

            const inventoryAuditTrails: MobilityInventoryStockUpdatedAuditTrail[] = (
                await loaders.auditTrailsByInventoryId.load(root.inventoryId)
            ).filter(
                trail =>
                    trail._kind === AuditTrailKind.MobilityInventoryStockUpdated &&
                    trail.stockIdentifier === root.stockIdentifier
            ) as MobilityInventoryStockUpdatedAuditTrail[];

            const index = inventoryAuditTrails.findIndex(trail => trail._id.equals(root._id));
            const inventory = inventoryAuditTrails[index];

            const keys = ['vin', 'period', 'price', 'mileage', 'isActive'] as const;

            return keys
                .map(inventoryKey => {
                    const field = t(`inventoryDetails:stocks.${inventoryKey}.label`);

                    if (index === 0) {
                        if (
                            isNil(inventory[inventoryKey]) ||
                            (!isBoolean(inventory[inventoryKey]) && isEmpty(inventory[inventoryKey]))
                        ) {
                            return null;
                        }

                        if (inventoryKey === 'period') {
                            return t(`auditTrails:inventory.${root._kind}_add`, {
                                author,
                                newValue: getTranslatedPeriod(inventory[inventoryKey], t),
                                field,
                                identifier: root.stockIdentifier,
                            });
                        }

                        if (inventoryKey === 'isActive') {
                            return t(`auditTrails:inventory.${root._kind}_add`, {
                                author,
                                newValue: getYesNoTranslation(inventory[inventoryKey], t),
                                field,
                                identifier: root.stockIdentifier,
                            });
                        }

                        return t(`auditTrails:inventory.${root._kind}_add`, {
                            author,
                            newValue: inventory[inventoryKey],
                            field,
                            identifier: root.stockIdentifier,
                        });
                    }
                    const oldInventory = inventoryAuditTrails[index - 1];

                    if (inventoryKey === 'period') {
                        const currentPeriod = oldInventory[inventoryKey];
                        const newPeriod = inventory[inventoryKey];

                        if (
                            currentPeriod?.start?.getTime() === newPeriod?.start?.getTime() &&
                            currentPeriod?.end?.getTime() === newPeriod?.end?.getTime()
                        ) {
                            return null;
                        }
                    }

                    if (oldInventory[inventoryKey] === inventory[inventoryKey]) {
                        return null;
                    }

                    if (inventoryKey === 'period') {
                        return t(
                            `auditTrails:inventory.${root._kind}${getInventoryTranslationSuffix(
                                oldInventory[inventoryKey],
                                inventory[inventoryKey]
                            )}`,
                            {
                                author,
                                oldValue: getTranslatedPeriod(oldInventory[inventoryKey], t),
                                newValue: getTranslatedPeriod(inventory[inventoryKey], t),
                                field,
                                identifier: root.stockIdentifier,
                            }
                        );
                    }

                    if (inventoryKey === 'isActive') {
                        return t(
                            `auditTrails:inventory.${root._kind}${getInventoryTranslationSuffix(
                                oldInventory[inventoryKey],
                                inventory[inventoryKey]
                            )}`,
                            {
                                author,
                                oldValue: getYesNoTranslation(oldInventory[inventoryKey], t),
                                newValue: getYesNoTranslation(inventory[inventoryKey], t),
                                field,
                                identifier: root.stockIdentifier,
                            }
                        );
                    }

                    return t(
                        `auditTrails:inventory.${root._kind}${getInventoryTranslationSuffix(
                            oldInventory[inventoryKey],
                            inventory[inventoryKey]
                        )}`,
                        {
                            author,
                            oldValue: oldInventory[inventoryKey],
                            newValue: inventory[inventoryKey],
                            field: inventoryKey,
                            identifier: root.stockIdentifier,
                        }
                    );
                })
                .filter(Boolean)
                .join('\n');
        }

        case AuditTrailKind.StockUpdated: {
            const { changes, author } = root;
            const updates = (changes ?? [])
                .map(change => {
                    const { label } = change;
                    const from = isNil(change.from) ? t('auditTrails:formats.empty') : change.from;
                    const to = isNil(change.to) ? t('auditTrails:formats.empty') : change.to;

                    return t('auditTrails:formats.change', { label, from, to });
                })
                .join(', ');

            const authors = await formatAuthor(author, t, loaders);

            return t(`auditTrails:stock.${root._kind}`, { author: formatAuthorText(t, authors), updates });
        }

        case AuditTrailKind.StockAmendments: {
            const { inventoryId, stockId } = root;
            const [author] = await formatAuthor(root.author, t, loaders);
            const inventory = await loaders.inventoryById.load(inventoryId);
            const module = await loaders.moduleById.load(inventory.moduleId);
            const company = await loaders.companyById.load(module.companyId);
            const { formatAmountWithCurrency } = getFormats(
                company?.currency || '',
                company?.roundings?.amount?.decimals || 0,
                company?.roundings?.percentage?.decimals || 0
            );

            const stock = inventory.stocks.find(i => i._id.equals(stockId));

            const { changes } = root;

            return (changes ?? [])
                .map(change => {
                    const { key, before, after, kind } = change;

                    switch (kind) {
                        case ChangeKind.String: {
                            const getValue = (value: string | null | undefined) =>
                                isNil(value) ? t('auditTrails:formats.empty') : value;

                            return t(
                                `auditTrails:inventory.${root._kind}${getInventoryStockTranslationSuffix(
                                    before,
                                    after
                                )}`,
                                {
                                    author,
                                    oldValue: getValue(before),
                                    newValue: getValue(after),
                                    field: t(key),
                                    identifier: stock.identifier,
                                }
                            );
                        }
                        case ChangeKind.Boolean: {
                            const getValue = (value: boolean | undefined | null) =>
                                isNil(value) ? t('auditTrails:formats.empty') : getYesNoTranslation(value, t);

                            return t(
                                `auditTrails:inventory.${root._kind}${getInventoryStockTranslationSuffix(
                                    before,
                                    after
                                )}`,
                                {
                                    author,
                                    oldValue: getValue(before),
                                    newValue: getValue(after),
                                    field: t(key),
                                    identifier: stock.identifier,
                                }
                            );
                        }

                        case ChangeKind.Currency: {
                            const getValue = value =>
                                isNil(value) ? t('auditTrails:formats.empty') : formatAmountWithCurrency(value);

                            return t(
                                `auditTrails:inventory.${root._kind}${getInventoryStockTranslationSuffix(
                                    before,
                                    after
                                )}`,
                                {
                                    author,
                                    oldValue: getValue(before),
                                    newValue: getValue(after),
                                    field: t(key),
                                    identifier: stock.identifier,
                                }
                            );
                        }
                        case ChangeKind.Period: {
                            const getValue = (value: Period) =>
                                isNil(value?.start) || isNil(value?.end)
                                    ? t('auditTrails:formats.empty')
                                    : getTranslatedPeriodByTimeZone(value as Period, t, company.timeZone);

                            return t(
                                `auditTrails:inventory.${root._kind}${getInventoryStockTranslationSuffix(
                                    before,
                                    after
                                )}`,
                                {
                                    author,
                                    oldValue: getValue(before),
                                    newValue: getValue(after),
                                    field: t(key),
                                    identifier: stock?.identifier,
                                }
                            );
                        }

                        case ChangeKind.BlockPeriod: {
                            const iterate = i =>
                                isNil(i?.start) || isNil(i?.end)
                                    ? t('auditTrails:formats.empty')
                                    : `${getTranslatedPeriodByTimeZone(i as Period, t, company.timeZone)}(${
                                          i.comment
                                      })`;

                            const getValue = value => value.map(iterate).join('/');

                            return t(
                                `auditTrails:inventory.${root._kind}${getInventoryStockTranslationSuffix(
                                    before,
                                    after
                                )}`,
                                {
                                    author,
                                    oldValue: getValue(before),
                                    newValue: getValue(after),
                                    field: t(key),
                                    identifier: stock?.identifier,
                                }
                            );
                        }

                        case ChangeKind.UploadedFileWithPreview: {
                            const getValue = value =>
                                isNil(value) ? t('auditTrails:formats.empty') : value.map(i => i.filename).join('/');

                            return t(
                                `auditTrails:inventory.${root._kind}${getInventoryStockTranslationSuffix(
                                    before,
                                    after
                                )}`,
                                {
                                    author,
                                    oldValue: getValue(before),
                                    newValue: getValue(after),
                                    field: t(key),
                                    identifier: stock?.identifier,
                                }
                            );
                        }

                        default:
                            return null;
                    }
                })
                .filter(Boolean)
                .join('\n');
        }

        case AuditTrailKind.InventoryAmendments: {
            const { inventoryId } = root;
            const [author] = await formatAuthor(root.author, t, loaders);
            const inventory = await loaders.inventoryById.load(inventoryId);
            const module = await loaders.moduleById.load(inventory.moduleId);
            const company = await loaders.companyById.load(module.companyId);

            const { changes } = root;

            return (changes ?? [])
                .map(change => {
                    const { key, before, after, kind } = change;

                    switch (kind) {
                        case ChangeKind.Boolean: {
                            const getValue = value =>
                                isNil(value) ? t('auditTrails:formats.empty') : getYesNoTranslation(value, t);

                            return t(
                                `auditTrails:inventory.${root._kind}${getInventoryStockTranslationSuffix(
                                    before,
                                    after
                                )}`,
                                {
                                    author,
                                    oldValue: getValue(before),
                                    newValue: getValue(after),
                                    field: t(key),
                                }
                            );
                        }

                        case ChangeKind.Period: {
                            const getValue = (value: Period) =>
                                isNil(value?.start) || isNil(value?.end)
                                    ? t('auditTrails:formats.empty')
                                    : getTranslatedPeriodByTimeZone(value as Period, t, company.timeZone);

                            return t(
                                `auditTrails:inventory.${root._kind}${getInventoryStockTranslationSuffix(
                                    before,
                                    after
                                )}`,
                                {
                                    author,
                                    oldValue: getValue(before),
                                    newValue: getValue(after),
                                    field: t(key),
                                }
                            );
                        }

                        default:
                            return null;
                    }
                })
                .filter(Boolean)
                .join('\n');
        }

        case AuditTrailKind.CustomerAmendments: {
            const { customerId, stage } = root;
            const customer = await loaders.customerById.load(customerId);
            const module = await loaders.moduleById.load(customer.moduleId);
            const company = await loaders.companyById.load(module.companyId);
            const { formatAmount, formatAmountWithCurrency, formatPercentage } = getFormats(
                company?.currency || '',
                company?.roundings?.amount?.decimals || 0,
                company?.roundings?.percentage?.decimals || 0
            );

            const { changes } = root;
            const updates = getUpdate(changes, {
                t,
                formatPercentage,
                formatAmount,
                formatAmountWithCurrency,
                company,
            });

            const [author] = await formatAuthor(root.author, t, loaders);

            if (stage) {
                return t(`auditTrails:customer.${root._kind}WithStage`, {
                    author,
                    updates,
                    stage,
                });
            }

            return t(`auditTrails:customer.${root._kind}`, {
                author,
                updates,
            });
        }

        case AuditTrailKind.CustomerComment: {
            const [author] = await formatAuthor(root.author, t, loaders);

            return t(`auditTrails:customer.${root._kind}`, { author, comment: root.comment });
        }
        case AuditTrailKind.CapBPIsExist:
        case AuditTrailKind.CapLeadIsExist:
        case AuditTrailKind.CapLeadCampaignNotFound:
        case AuditTrailKind.CapConsentNotFound:
            return t(`auditTrails:application.${root._kind}`, {
                id: root.id,
            });

        case AuditTrailKind.CapBPCreated:
        case AuditTrailKind.CapBPUpdated:
        case AuditTrailKind.CapCompetitorVehicleCreated:
        case AuditTrailKind.CapCompetitorVehicleUpdated:
        case AuditTrailKind.CapLeadCreated:
        case AuditTrailKind.CapLeadUpdated:
        case AuditTrailKind.CapActivitySubmitted:
        case AuditTrailKind.CapActivityEndTestDriveSubmitted:
        case AuditTrailKind.CapActivityPlannedTestDriveSubmitted:
        case AuditTrailKind.CapActivityPlannedShowroomVisitSubmitted:
        case AuditTrailKind.CapActivityCompleteShowroomVisitSubmitted:
        case AuditTrailKind.CapConsentSubmitted:
        case AuditTrailKind.CapCustomerAttributeCreated:
        case AuditTrailKind.CapCustomerAttributeUpdated:
        case AuditTrailKind.CapCustomerAttributeDeleted:
            return t(`auditTrails:application.${root._kind}.${root.success ? 'success' : 'failed'}`, {
                id: root.id,
                errorMessage: root.errorMessage || '',
            });

        case AuditTrailKind.CapBPSearchFailed:
        case AuditTrailKind.CapLeadSearchFailed:
        case AuditTrailKind.CapCustomerAttributeSearchFailed:
            return t(`auditTrails:application.${root._kind}`, {
                errorMessage: root.errorMessage,
            });

        case AuditTrailKind.TradeInDraftRequestReceived:
        case AuditTrailKind.TradeInDraftRequestSent:
        case AuditTrailKind.TradeInDrafted:
        case AuditTrailKind.TradeInBidCloseRequestReceived:
        case AuditTrailKind.TradeInBidCloseRequestSent:
            return t(`auditTrails:tradeIn.${root._kind}`);

        case AuditTrailKind.TradeInDraftRequestSendingFailed:
        case AuditTrailKind.TradeInBidCloseRequestSendingFailed: {
            return t(`auditTrails:tradeIn.${root._kind}`, {
                reason: root.reason,
            });
        }

        case AuditTrailKind.TradeInConfirmationRequestSendingFailed: {
            return t(`auditTrails:tradeIn.${root._kind}.${root.awarded ? 'awarded' : 'notAwarded'}`, {
                reason: root.reason,
            });
        }

        case AuditTrailKind.TradeInConfirmationRequestReceived:
        case AuditTrailKind.TradeInConfirmationRequestSent: {
            return t(`auditTrails:tradeIn.${root._kind}.${root.awarded ? 'awarded' : 'notAwarded'}`);
        }

        case AuditTrailKind.TradeInComment: {
            const [author] = await formatAuthor(root.author, t, loaders);

            return t(`auditTrails:tradeIn.${root._kind}`, { author, comment: root.comment });
        }

        case AuditTrailKind.LeadQualified:
        case AuditTrailKind.LeadUnqualified:
        case AuditTrailKind.LeadContacted:
        case AuditTrailKind.LeadLost:
        case AuditTrailKind.LeadCompleted:
        case AuditTrailKind.LeadDrafted:
        case AuditTrailKind.LeadCustomerAgreedOnCnD:
        case AuditTrailKind.LeadShared:
        case AuditTrailKind.LeadIntentAndAssign:
        case AuditTrailKind.LeadTestDriveCreated:
        case AuditTrailKind.LeadShowroomVisitBooked:
        case AuditTrailKind.LeadStockAssigned: {
            const [author] = await formatAuthor(root.author, t, loaders);

            return t(`auditTrails:leads.${root._kind}`, { author });
        }

        case AuditTrailKind.LeadSubmittedToSystem:
        case AuditTrailKind.LeadSubmittedWithError:
        case AuditTrailKind.LeadSubmissionFailed:
        case AuditTrailKind.LeadKYCReceived:
            return t(`auditTrails:leads.${root._kind}`);

        case AuditTrailKind.LeadAmended: {
            const { changes, leadId } = root;
            const lead = await loaders.leadById.load(leadId);
            const module = await loaders.moduleById.load(lead.moduleId);
            const company = await loaders.companyById.load(module.companyId);

            const { formatAmount, formatAmountWithCurrency, formatPercentage } = getFormats(
                company?.currency || '',
                company?.roundings?.amount?.decimals || 0,
                company?.roundings?.percentage?.decimals || 0
            );

            const updates = getUpdate(changes, {
                t,
                formatPercentage,
                formatAmount,
                formatAmountWithCurrency,
                company,
            });

            const [author] = await formatAuthor(root.author, t, loaders);

            return t(`auditTrails:leads.${root._kind}`, {
                author,
                updates,
            });
        }

        case AuditTrailKind.LeadEmailSent: {
            const [recipient, recipientType] = await fromAudience(root.audience, t, loaders, root.leadId, null);

            return recipient
                ? t(`auditTrails:leads.${root._kind}`, { recipient, recipientType })
                : t(`auditTrails:leads.leadCommonEmailSent`, { recipientType });
        }

        case AuditTrailKind.LeadFollowedUp: {
            const { scheduledDate, remarks, leadId } = root;
            const lead = await loaders.leadById.load(leadId);
            const module = await loaders.moduleById.load(lead.moduleId);
            const company = await loaders.companyById.load(module.companyId);

            const formattedDate = getTranslatedDateTimeByTimeZone(scheduledDate, t, company.timeZone);

            const [author] = await formatAuthor(root.author, t, loaders);

            return t(`auditTrails:leads.${root._kind}${remarks ? 'WithRemarks' : ''}`, {
                author,
                scheduledDate: formattedDate,
                remarks,
            });
        }

        case AuditTrailKind.LeadCreatedByPorscheRetain:
        case AuditTrailKind.LeadUpdatedByPorscheRetain: {
            return t(`auditTrails:leads.${root._kind}`);
        }

        default:
            return '';
    }
};

const resolver: GraphQLAuditTrailResolvers = {
    id: root => root._id,
    date: root => root._date,
    message: renderAuditTrailMessage,
    kind: root => root._kind,
    customerKind: root => (root._kind === AuditTrailKind.ApplicationCustomerAgreedOnCnD ? root.customerKind : null),
};

export default resolver;
