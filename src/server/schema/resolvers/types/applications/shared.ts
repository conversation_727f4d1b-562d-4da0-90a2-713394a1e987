import { isEmpty, isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import urlJoin from 'url-join';
import config from '../../../../core/config';
import {
    type Application,
    type ApplicationJourney,
    type ApplicationJourneyAgreements,
    ApplicationJourneyPaymentGateway,
    ApplicationJourneySigningMode,
    ApplicationKind,
    ApplicationStage,
    type ApplicationWithDocuments,
    type ApplicationWithInsurance,
    type ConditionContext,
    ConsentsAndDeclarations,
    ConsentsAndDeclarationsType,
    type GiftVoucher,
    type GiftVoucherModule,
    MarketingPlatform,
    ModuleType,
    SalesOffer,
    SalesOfferModule,
    SettingId,
    getCustomerModuleFromApplication,
    getKYCFieldsFromPresets,
    getLocalCustomerAggregatedFields,
    getTestDriveKYCPresetsForApplication,
    hasShowroomVisitCondition,
    hasTestDriveProcessCondition,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { getInitialAgreementsForSalesOffer } from '../../../../database/helpers/agreements';
import getAgreementsForApplication from '../../../../database/helpers/getAgreementsForApplication';
import getInitialAgreementsForApplication from '../../../../database/helpers/getInitialAgreementsForApplication';
import getInitialAgreementsForGiftVoucher from '../../../../database/helpers/getInitialAgreementsForGiftVoucher';
import { getSigningModule } from '../../../../journeys/helper/getSigningModule';
import { isValidApplicationModuleForAgreementPdf } from '../../../../journeys/helper/isValidApplicationForAgreement';
import createLoaders, { Loaders } from '../../../../loaders';
import type { GiftVoucherResults } from '../../../../utils/excel/giftVouchers/types/GiftVoucherResults';
import toNumberWithMinor from '../../../../utils/numberHelper';
import {
    ApplicationDocumentKind,
    LocalCustomerFieldKey,
    SalesOfferAgreementKind,
    SalesOfferFeatureKind,
} from '../../definitions';

export type AgreedConsentsPayload = ConsentsAndDeclarations & {
    isAgreed: boolean;
    platformsAgreed?: MarketingPlatform;
};

const prefillingAgreements = (
    agreement: ApplicationJourneyAgreements['agreements'][number],
    consent: ConsentsAndDeclarations
) => {
    switch (consent._type) {
        case ConsentsAndDeclarationsType.Text:
        case ConsentsAndDeclarationsType.Checkbox:
            return {
                ...consent,
                ...agreement,
                isAgreed: agreement.isAgreed,
            };

        case ConsentsAndDeclarationsType.Marketing: {
            return {
                ...consent,
                ...agreement,
                isAgreed: agreement.isAgreed,
                platformsAgreed: consent.platform || agreement.platformsAgreed,
            };
        }

        default:
            throw new Error('Consent and declaration type is not supported');
    }
};

const getApplicationSigningModule = async (
    bankId: ObjectId,
    withFinancing: boolean,
    suiteId: ObjectId,
    loaders: Loaders
) => {
    if (!bankId || !withFinancing) {
        return null;
    }

    const bank = await loaders.systemBankById.load(bankId);
    const journey = await loaders.applicationJourneyBySuiteId.load(suiteId);
    const isResubmit = !isNil(journey.submission);

    const signingModuleId = isResubmit ? bank.reSubmissionApprovalModuleId : bank.submissionApprovalModuleId;

    if (!signingModuleId) {
        return null;
    }

    const module = await loaders.moduleById.load(signingModuleId);

    if (!module) {
        return null;
    }

    return module;
};

const retrieveApplicationDocumentKind = (kind: ApplicationDocumentKind) => {
    switch (kind) {
        case ApplicationDocumentKind.KYCLicenseUpload:
            return LocalCustomerFieldKey.UploadDrivingLicense;

        case ApplicationDocumentKind.KYCIdentityUpload:
            return LocalCustomerFieldKey.UploadIdentity;

        case ApplicationDocumentKind.KYCOtherDocumentUpload:
            return LocalCustomerFieldKey.UploadOtherDocument;

        case ApplicationDocumentKind.KYCPassportUpload:
            return LocalCustomerFieldKey.UploadPassport;

        default:
            return null;
    }
};

/**
 * validating the testDriveKYC and testDriveAgreements are all filled and checked
 * @param journey Application Journey
 * @param loaders cached loaders
 * @returns move to TestDriveStep / display Test Drive KYC page if true
 */
export const hasTestDriveKYCandConsents = async (journey: ApplicationJourney, loaders: Loaders) => {
    const application = await loaders.applicationBySuiteId.load(journey.applicationSuiteId);

    const testDriveKYCPresets = await getTestDriveKYCPresetsForApplication(application);
    const unfilteredDriveAgreements = await getTestDriveAgreements(application);

    // filter out only mandatory agreement for validation
    const testDriveAgreements = unfilteredDriveAgreements.filter(
        agreement =>
            (agreement._type === ConsentsAndDeclarationsType.Checkbox ||
                agreement._type === ConsentsAndDeclarationsType.Marketing) &&
            agreement.isMandatory
    );

    if (testDriveKYCPresets.length === 0 && testDriveAgreements.length === 0) {
        return false;
    }

    const customer = await loaders.customerById.load(application.applicantId);
    // Key value pairs for kyc field values
    const fields = getLocalCustomerAggregatedFields(customer);

    const customerModule = await getCustomerModuleFromApplication(application);
    // Array of test drive KYC fields in order
    const testDriveKYCFields = getKYCFieldsFromPresets(
        customerModule.kycFields.sort((a, b) => a.order - b.order),
        testDriveKYCPresets
    );

    const documents = application.documents.filter(document =>
        testDriveKYCFields.find(
            field =>
                !isNil(retrieveApplicationDocumentKind(document.kind)) &&
                field.key === retrieveApplicationDocumentKind(document.kind)
        )
    );

    /**
     * prefilling the test drive KYC with mandatory fields only
     * when all mandatory fields all filled, skip KYC page
     */
    const preFilledTestDriveKYC = testDriveKYCFields
        .filter(field => field.isRequired)
        .map(field => {
            const documentUpload = documents.find(
                document => retrieveApplicationDocumentKind(document.kind) === field.key
            );
            if (documentUpload) {
                return {
                    key: field.key,
                    value: documentUpload,
                };
            }

            return {
                key: field.key,
                value: fields[field.key],
            };
        });

    const testDriveKYCFilled = !isNil(journey.testDriveKYC) && journey.testDriveKYC?.completed;
    /**
     * we need to let EventApplication always true due to the KYC Preset is reusing from
     * Event Details Customer Information, when validation take place on same KYC Presets, it always
     * return completed (false in validation below).
     * In this scenario, we unable to update the `TestDriveKYCStep` as when validation return false, it unable to reach
     * TestDriveKYC Step
     *
     * Initiall ::
     * Before KYC UI update
     *
     * VALIDATION = return true
     * TestDriveAgreementStep = registered
     * TestDriveKYCStep = registered
     *
     *
     * After KYC UI updated
     * TestDriveAgreementStep = registered (passed after submitTestDriveAgreement submitted)
     * IN THIS STEP :: VALIDATION return false as KYCPreset  false and Agreements false
     * TestDriveKYCStep = registered but unable to enter step
     *
     * :: In short
     * EventApplication case should always return true for validation in order to reach TestDriveKYCStep
     */

    const testDriveKycStep =
        application.kind === ApplicationKind.Event
            ? !isNil(journey?.testDriveKYC) &&
              preFilledTestDriveKYC.length > 0 &&
              preFilledTestDriveKYC.some(testDrive => isEmpty(testDrive.value) || isNil(testDrive.value))
            : (!isNil(journey?.testDriveKYC) || !testDriveKYCFilled) &&
              preFilledTestDriveKYC.length > 0 &&
              preFilledTestDriveKYC.some(testDrive => isEmpty(testDrive.value) || isNil(testDrive.value));

    const hasEmptyOrUncheckedFromCustomerKYCBasedOnTestDriveKYC =
        testDriveKycStep ||
        (testDriveAgreements.length > 0 && testDriveAgreements?.some(agreement => !agreement.isAgreed));

    return hasEmptyOrUncheckedFromCustomerKYCBasedOnTestDriveKYC;
};

const getAgreements = (
    application: Application,
    journey: ApplicationJourney,
    customerKind: ConditionContext['customerKind'],
    loaders = createLoaders()
) => {
    switch (customerKind) {
        case 'guarantor': {
            return journey.guarantorAgreements
                ? journey.guarantorAgreements.agreements
                : getInitialAgreementsForApplication(application, customerKind, loaders);
        }

        default: {
            return journey.applicantAgreements
                ? journey.applicantAgreements.agreements
                : getInitialAgreementsForApplication(application, customerKind, loaders);
        }
    }
};

export const getGiftVoucherAgreements = async (
    giftVoucher: GiftVoucher | GiftVoucherResults,
    customerKind: ConditionContext['customerKind'],
    loaders = createLoaders()
) => {
    const { collections } = await getDatabaseContext();

    const giftVoucherModule = (await collections.modules.findOne({ _id: giftVoucher.moduleId })) as GiftVoucherModule;
    if (!giftVoucherModule) {
        // without module there's no agreement
        return [];
    }
    const journey = await loaders.giftVoucherJourneyBySuiteId.load(giftVoucher._versioning.suiteId);

    const agreements = journey.applicantAgreements
        ? journey.applicantAgreements.agreements
        : await getInitialAgreementsForGiftVoucher(giftVoucher, customerKind);

    return agreements.map(async agreement => {
        const consent = await loaders.consentById.load(agreement.consentId);

        return prefillingAgreements(agreement, consent);
    });
};

export const getSalesOfferAgreements = async (
    salesOffer: SalesOffer,
    feature: SalesOfferFeatureKind,
    customerKind: ConditionContext['customerKind'],
    salesOfferAgreementFeature: SalesOfferAgreementKind = null,
    loaders = createLoaders()
) => {
    const { collections } = await getDatabaseContext();

    const salesOfferModule = (await collections.modules.findOne({ _id: salesOffer.moduleId })) as SalesOfferModule;
    if (!salesOfferModule) {
        // without module there's no agreement
        return [];
    }

    const agreements = await getInitialAgreementsForSalesOffer(
        salesOffer,
        feature,
        customerKind,
        salesOfferAgreementFeature
    );

    return Promise.all(
        agreements.map(async agreement => {
            const consent = await loaders.consentById.load(agreement.consentId);

            return prefillingAgreements(agreement, consent);
        })
    );
};

export const getTestDriveAgreements = async (application: Application, loaders = createLoaders()) => {
    const journey = await loaders.applicationJourneyBySuiteId.load(application._versioning.suiteId);

    const agreements = await getAgreementsForApplication(application, 'local', loaders);

    const applicantConsents = journey.applicantAgreements?.agreements;
    const filteredAgreements = agreements
        .filter(agreement => hasTestDriveProcessCondition(agreement.conditions))
        .map((item): ApplicationJourneyAgreements['agreements'][number] => {
            switch (item._type) {
                case ConsentsAndDeclarationsType.Text:
                    return {
                        consentId: item._id,
                        isAgreed:
                            applicantConsents?.find(consent => consent.consentId.equals(item._id))?.isAgreed || false,
                        date: new Date(),
                        purpose: item.purpose,
                    };

                case ConsentsAndDeclarationsType.Checkbox:
                    return {
                        consentId: item._id,
                        isAgreed:
                            applicantConsents?.find(consent => consent.consentId.equals(item._id))?.isAgreed || false,
                        date: new Date(),
                        purpose: item.purpose,
                        isMandatory: item.isMandatory,
                    };

                case ConsentsAndDeclarationsType.Marketing: {
                    const marketConsent = applicantConsents?.find(consent => consent.consentId.equals(item._id));

                    return {
                        consentId: item._id,
                        isAgreed: marketConsent?.isAgreed || false,
                        date: new Date(),
                        purpose: item.purpose,
                        isMandatory: item.isMandatory,
                        platformsAgreed: {
                            email: marketConsent?.platformsAgreed?.email || false,
                            fax: marketConsent?.platformsAgreed?.fax || false,
                            mail: marketConsent?.platformsAgreed?.mail || false,
                            phone: marketConsent?.platformsAgreed?.phone || false,
                            sms: marketConsent?.platformsAgreed?.sms || false,
                        },
                    };
                }

                default:
                    throw new Error('Application agreement type is not supported');
            }
        });

    const journeyAgreements = journey?.testDriveAgreements
        ? journey.testDriveAgreements?.agreements
        : filteredAgreements;

    return Promise.all(
        journeyAgreements.map(async agreement => {
            const consent = await loaders.consentById.load(agreement.consentId);

            return prefillingAgreements(agreement, consent);
        })
    );
};

export const getShowroomVisitAgreements = async (application: Application, loaders = createLoaders()) => {
    const journey = await loaders.applicationJourneyBySuiteId.load(application._versioning.suiteId);

    const agreements = await getAgreementsForApplication(application, 'local', loaders);

    const applicantConsents = journey.applicantAgreements?.agreements;
    const filteredAgreements = agreements
        .filter(agreement => hasShowroomVisitCondition(agreement.conditions))
        .map((item): ApplicationJourneyAgreements['agreements'][number] => {
            const marketConsent = applicantConsents?.find(consent => consent.consentId.equals(item._id));

            const baseConsentValues = {
                consentId: item._id,
                isAgreed: marketConsent?.isAgreed || false,
                date: new Date(),
                purpose: item.purpose,
            };

            switch (item._type) {
                case ConsentsAndDeclarationsType.Text:
                    return baseConsentValues;

                case ConsentsAndDeclarationsType.Checkbox:
                    return {
                        ...baseConsentValues,
                        isMandatory: item.isMandatory,
                    };

                case ConsentsAndDeclarationsType.Marketing: {
                    return {
                        ...baseConsentValues,
                        isMandatory: item.isMandatory,
                        platformsAgreed: {
                            email: marketConsent?.platformsAgreed?.email || false,
                            fax: marketConsent?.platformsAgreed?.fax || false,
                            mail: marketConsent?.platformsAgreed?.mail || false,
                            phone: marketConsent?.platformsAgreed?.phone || false,
                            sms: marketConsent?.platformsAgreed?.sms || false,
                        },
                    };
                }

                default:
                    throw new Error('Application agreement type is not supported');
            }
        });

    const journeyAgreements = journey?.showroomVisitAgreements
        ? journey.showroomVisitAgreements?.agreements
        : filteredAgreements;

    return Promise.all(
        journeyAgreements.map(async agreement => {
            const consent = await loaders.consentById.load(agreement.consentId);

            switch (consent._type) {
                case ConsentsAndDeclarationsType.Text:
                case ConsentsAndDeclarationsType.Checkbox:
                    return {
                        ...consent,
                        isAgreed: agreement.isAgreed,
                    };

                case ConsentsAndDeclarationsType.Marketing:
                    return { ...consent, isAgreed: agreement.isAgreed, platformsAgreed: agreement.platformsAgreed };

                default:
                    throw new Error('Consent and declaration type is not supported');
            }
        })
    );
};

export const getApplicantAgreements = async (
    root: Application,
    customerKind: ConditionContext['customerKind'] = 'local',
    loaders = createLoaders()
) => {
    // get the journey
    const journey = await loaders.applicationJourneyBySuiteId.load(root._versioning.suiteId);

    const agreements = await getAgreements(root, journey, customerKind, loaders);

    return Promise.all(
        agreements.map(async agreement => {
            const consent = await loaders.consentById.load(agreement.consentId);

            return prefillingAgreements(agreement, consent);
        })
    );
};

export const getApplicationDeposit = async (
    { _id, moduleId, dealerId = undefined, _versioning },
    args,
    { loaders }
) => {
    // we must use database journey instead of loader
    // loader cant fetch the new application details
    const { collections } = await getDatabaseContext();
    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: _versioning.suiteId,
    });

    if (!journey || !journey.deposit || !journey.deposit.settingId) {
        return undefined;
    }

    const applicationModule = await loaders.moduleById.load(moduleId);

    // necessary configuration is missing
    if (
        !applicationModule ||
        ![
            ModuleType.StandardApplicationModule,
            ModuleType.ConfiguratorModule,
            ModuleType.MobilityModule,
            ModuleType.FinderApplicationPublicModule,
            ModuleType.FinderApplicationPrivateModule,
        ].includes(applicationModule._type)
    ) {
        return undefined;
    }

    const setting = await loaders.settingById.load(journey.deposit.settingId);
    if (!setting) {
        return undefined;
    }

    switch (setting.settingId) {
        case SettingId.AdyenPayment: {
            if (journey.deposit.gateway !== ApplicationJourneyPaymentGateway.Adyen) {
                throw new Error('Unexpected gateway kind');
            }

            return {
                clientKey: setting.secrets.clientKey,
                environment: setting.environment,
                sessionId: journey.deposit?.session.id,
                sessionData: journey.deposit?.session.data,
                paymentMethod: journey.deposit?.paymentMethod,
                transactionId: journey.deposit?.transactionId,
                completedAt: journey.deposit?.completedAt,
                amount: !isNil(journey.deposit?.amount)
                    ? journey.deposit?.amount
                    : (applicationModule.depositAmount.overrides?.find(({ dealerId: inputId }) =>
                          inputId.equals(dealerId)
                      )?.value ?? applicationModule.depositAmount.defaultValue),
                status: journey.deposit?.status,
                skipped: journey.deposit?.skipped,
                gateway: ApplicationJourneyPaymentGateway.Adyen,
            };
        }

        case SettingId.PorschePayment: {
            if (journey.deposit.gateway !== ApplicationJourneyPaymentGateway.Porsche) {
                throw new Error('Unexpected gateway kind');
            }

            const link = await loaders.porschePaymentRedirectionLinkByApplicationId.load(_id);

            const amount = !isNil(journey.deposit?.amount)
                ? journey.deposit?.amount
                : (applicationModule.depositAmount.overrides?.find(({ dealerId: inputId }) => inputId.equals(dealerId))
                      ?.value ?? applicationModule.depositAmount.defaultValue);

            return {
                apiKey: setting.secrets.apiKey,
                environment: setting.environment,
                amount,
                minorUnitsAmount: toNumberWithMinor(amount, setting.currency),
                paymentMethod: journey.deposit?.paymentMethod,
                transactionId: journey.deposit?.transactionId,
                completedAt: journey.deposit?.completedAt,
                status: journey.deposit?.status,
                skipped: journey.deposit?.skipped,
                gateway: ApplicationJourneyPaymentGateway.Porsche,
                redirectUrl: urlJoin(config.applicationEndpoint, `/.callback/porschePayment/${link?.secret}`),
                widgetUrl: setting.secrets.widgetUrl,
                assortment: journey.deposit?.assortment,
            };
        }

        case SettingId.FiservPayment: {
            if (journey.deposit.gateway !== ApplicationJourneyPaymentGateway.Fiserv) {
                throw new Error('Unexpected gateway kind');
            }

            return {
                storeName: setting.secrets.storeName,
                amount: !isNil(journey.deposit?.amount)
                    ? journey.deposit?.amount
                    : (applicationModule.depositAmount.overrides?.find(({ dealerId: inputId }) =>
                          inputId.equals(dealerId)
                      )?.value ?? applicationModule.depositAmount.defaultValue),
                paymentMethod: journey.deposit?.paymentMethod,
                transactionId: journey.deposit?.transactionId,
                completedAt: journey.deposit?.completedAt,
                status: journey.deposit?.status,
                skipped: journey.deposit?.skipped,
                gateway: ApplicationJourneyPaymentGateway.Fiserv,
            };
        }

        case SettingId.PayGatePayment: {
            if (journey.deposit.gateway !== ApplicationJourneyPaymentGateway.PayGate) {
                throw new Error('Unexpected gateway kind');
            }

            return {
                apiKey: setting.secrets.apiKey,
                amount: !isNil(journey.deposit?.amount)
                    ? journey.deposit?.amount
                    : (applicationModule.depositAmount.overrides?.find(({ dealerId: inputId }) =>
                          inputId.equals(dealerId)
                      )?.value ?? applicationModule.depositAmount.defaultValue),
                paymentMethod: journey.deposit?.paymentMethod,
                transactionId: journey.deposit?.transactionId,
                completedAt: journey.deposit?.completedAt,
                status: journey.deposit?.status,
                skipped: journey.deposit?.skipped,
                gateway: ApplicationJourneyPaymentGateway.PayGate,
            };
        }

        case SettingId.TtbPayment: {
            if (journey.deposit.gateway !== ApplicationJourneyPaymentGateway.Ttb) {
                throw new Error('Unexpected gateway kind');
            }

            return {
                apiKey: setting.secrets.apiKey,
                amount: !isNil(journey.deposit?.amount)
                    ? journey.deposit?.amount
                    : (applicationModule.depositAmount.overrides?.find(({ dealerId: inputId }) =>
                          inputId.equals(dealerId)
                      )?.value ?? applicationModule.depositAmount.defaultValue),
                paymentMethod: journey.deposit?.paymentMethod,
                transactionId: journey.deposit?.transactionId,
                completedAt: journey.deposit?.completedAt,
                status: journey.deposit?.status,
                skipped: journey.deposit?.skipped,
                gateway: ApplicationJourneyPaymentGateway.Ttb,
            };
        }

        default:
            throw new Error(`Payment gateway not implemented`);
    }
};

export const getSigning = async (root: ApplicationWithDocuments, loaders = createLoaders()) => {
    const journey = await loaders.applicationJourneyBySuiteId.load(root._versioning.suiteId);
    const isResubmit = !isNil(journey.submission);

    const applicationModule = await loaders.moduleById.load(root.moduleId);
    if (!applicationModule || !isValidApplicationModuleForAgreementPdf(applicationModule)) {
        return null;
    }

    const module = await getSigningModule(applicationModule, root, isResubmit, loaders);
    if (!module) {
        return null;
    }

    switch (module._type) {
        case ModuleType.BasicSigningModule:
            return {
                kind: ApplicationJourneySigningMode.OTP,
            };

        case ModuleType.NamirialSigningModule:
        case ModuleType.Docusign: {
            if (!journey.applicantSigning) {
                // before signing step, applicantSigning value will not available
                return undefined;
            }

            if (
                journey.applicantSigning.kind !== ApplicationJourneySigningMode.Namirial &&
                journey.applicantSigning.kind !== ApplicationJourneySigningMode.Docusign
            ) {
                throw new Error('unexpected');
            }

            return {
                kind: journey.applicantSigning.kind,
                status: journey.applicantSigning.status,
                envelopeId: journey.applicantSigning.envelopeId,
                redirectionUrl: journey.applicantSigning.redirectionUrl,
            };
        }

        default:
            return null;
    }
};

export const getGuarantorSigning = async (root: ApplicationWithDocuments, loaders = createLoaders()) => {
    const journey = await loaders.applicationJourneyBySuiteId.load(root._versioning.suiteId);

    if (
        root.kind === ApplicationKind.Mobility ||
        root.kind === ApplicationKind.Event ||
        root.kind === ApplicationKind.Launchpad ||
        root.kind === ApplicationKind.SalesOffer ||
        !root.bankId
    ) {
        return null;
    }

    const module = await getApplicationSigningModule(
        root.bankId,
        root.configuration.withFinancing,
        root._versioning.suiteId,
        loaders
    );

    if (!module) {
        return null;
    }

    switch (module._type) {
        case ModuleType.BasicSigningModule:
            return {
                kind: ApplicationJourneySigningMode.OTP,
            };

        case ModuleType.NamirialSigningModule:
        case ModuleType.Docusign: {
            if (!journey.guarantorSigning) {
                // before signing step, applicantSigning value will not available
                return undefined;
            }

            if (
                journey.guarantorSigning.kind !== ApplicationJourneySigningMode.Namirial &&
                journey.guarantorSigning.kind !== ApplicationJourneySigningMode.Docusign
            ) {
                throw new Error('unexpected');
            }

            return {
                kind: journey.guarantorSigning.kind,
                status: journey.guarantorSigning.status,
                envelopeId: journey.guarantorSigning.envelopeId,
                redirectionUrl: journey.guarantorSigning.redirectionUrl,
            };
        }

        default:
            return null;
    }
};

export const getTestDriveSigning = async (root: ApplicationWithDocuments, loaders = createLoaders()) => {
    if (!root?._versioning?.suiteId || !root?.appointmentStage?.appointmentModuleId) {
        return null;
    }

    const journey = await loaders.applicationJourneyBySuiteId.load(root._versioning.suiteId);
    if (!journey) {
        return null;
    }

    const signingModule = journey.testDriveSetting?.signingModuleId
        ? await loaders.moduleById.load(journey.testDriveSetting.signingModuleId)
        : null;

    if (!signingModule) {
        return null;
    }

    switch (signingModule._type) {
        case ModuleType.BasicSigningModule:
            return {
                kind: ApplicationJourneySigningMode.OTP,
            };

        case ModuleType.NamirialSigningModule:
        case ModuleType.Docusign: {
            if (!journey.testDriveProcessSigning) {
                return null;
            }

            if (
                journey.testDriveProcessSigning.kind !== ApplicationJourneySigningMode.Namirial &&
                journey.testDriveProcessSigning.kind !== ApplicationJourneySigningMode.Docusign
            ) {
                throw new Error('unexpected');
            }

            return {
                kind: journey.testDriveProcessSigning.kind,
                status: journey.testDriveProcessSigning.status,
                envelopeId: journey.testDriveProcessSigning.envelopeId,
                redirectionUrl: journey.testDriveProcessSigning.redirectionUrl,
            };
        }

        default:
            return null;
    }
};

export const getInsuranceSigning = async (root: ApplicationWithInsurance, loaders: Loaders) => {
    if (!root.insurancing?.insurerId || !root.configuration.withInsurance) {
        return null;
    }

    const insurer = await loaders.insurerById.load(root.insurancing.insurerId);

    const journey = await loaders.applicationJourneyBySuiteId.load(root._versioning.suiteId);
    const isResubmit = !isNil(journey.submission);

    const signingModuleId = isResubmit ? insurer.reSubmissionApprovalModuleId : insurer.submissionApprovalModuleId;

    if (!signingModuleId) {
        return null;
    }

    const module = await loaders.moduleById.load(signingModuleId);

    if (!module) {
        return null;
    }

    switch (module._type) {
        case ModuleType.BasicSigningModule:
            return {
                kind: ApplicationJourneySigningMode.OTP,
            };

        case ModuleType.NamirialSigningModule:
        case ModuleType.Docusign: {
            if (!journey.insuranceApplicantSigning) {
                // before signing step, insuranceApplicantSigning value will not available
                return undefined;
            }

            if (
                journey.insuranceApplicantSigning.kind !== ApplicationJourneySigningMode.Namirial &&
                journey.insuranceApplicantSigning.kind !== ApplicationJourneySigningMode.Docusign
            ) {
                throw new Error('unexpected');
            }

            return {
                kind: journey.insuranceApplicantSigning.kind,
                status: journey.insuranceApplicantSigning.status,
                envelopeId: journey.insuranceApplicantSigning.envelopeId,
                redirectionUrl: journey.insuranceApplicantSigning.redirectionUrl,
            };
        }

        default:
            return null;
    }
};

export const applicationStageOrder = [
    ApplicationStage.Reservation,
    ApplicationStage.Appointment,
    ApplicationStage.VisitAppointment,
    ApplicationStage.Financing,
    ApplicationStage.Insurance,
    ApplicationStage.Mobility,
    ApplicationStage.TradeIn,
];

export const getLatestStage = (application: Application) => {
    let latestStageWithType = null;
    applicationStageOrder.forEach(stage => {
        const stagePath = `${stage}Stage`;
        if (!isNil(application[stagePath]) && !latestStageWithType) {
            const latestStage = application[stagePath];
            latestStageWithType = {
                ...latestStage,
                _kind: stage,
            };
        }
    });

    return latestStageWithType;
};

export const getTestDriveRedirectionUrl = async (application: Application, loaders = createLoaders()) => {
    const journey = await loaders.applicationJourneyBySuiteId.load(application._versioning.suiteId);

    if (!journey) {
        return null;
    }

    return journey.testDriveRedirectUrl;
};
